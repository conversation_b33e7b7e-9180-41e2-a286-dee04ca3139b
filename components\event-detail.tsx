"use client"

import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, FileText, Video, Star, Clock } from "lucide-react"

interface EventDetailProps {
  event: {
    id: string
    year: string
    title: string
    description: string
    icon: any
    category: string
    image: string
    significance: string
    detailContent: {
      fullDescription: string
      keyPoints: string[]
      videoUrl: string
      documents: string[]
    }
  }
  onBack: () => void
  isTransitioning: boolean
}

export function EventDetail({ event, onBack, isTransitioning }: EventDetailProps) {
  const Icon = event.icon

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-red-50 via-yellow-50 to-red-50 transition-all duration-500 ${
        isTransitioning ? "opacity-0 scale-95" : "opacity-100 scale-100"
      }`}
    >
      <div className="sticky top-0 z-40 bg-white/95 backdrop-blur-lg border-b border-red-200 shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <Button variant="ghost" size="sm" onClick={onBack} className="hover:bg-red-100 text-red-700 font-medium">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại Timeline
            </Button>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-red-100 text-red-800 border-red-200">
                {event.year}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <div className="max-w-5xl mx-auto">
          <div className="mb-8">
            <Card className="overflow-hidden border-2 border-red-200 shadow-lg">
              <div className="relative h-64 md:h-80">
                <img src={event.image || "/placeholder.svg"} alt={event.title} className="w-full h-full object-cover" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="p-1.5 bg-white/20 rounded-lg backdrop-blur-sm">
                        <Icon className="h-5 w-5 text-white" />
                      </div>
                      <Badge className="bg-yellow-500 text-yellow-900 border-0">{event.category}</Badge>
                    </div>
                    <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 text-balance">{event.title}</h1>
                    <p className="text-white/90 text-sm md:text-base max-w-2xl">{event.significance}</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Video Section - gọn hơn */}
              <Card className="p-4 border-red-200">
                <div className="flex items-center gap-2 mb-3">
                  <Video className="h-4 w-4 text-red-600" />
                  <h3 className="text-lg font-semibold text-red-800">Video tài liệu</h3>
                </div>
                <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                  <iframe
                    src={event.detailContent.videoUrl}
                    title={`Video về ${event.title}`}
                    className="w-full h-full"
                    allowFullScreen
                  />
                </div>
              </Card>

              {/* Description - gọn hơn */}
              <Card className="p-4 border-yellow-200">
                <h3 className="text-lg font-semibold mb-3 text-yellow-800">Mô tả chi tiết</h3>
                <p className="text-gray-700 leading-relaxed text-sm">{event.detailContent.fullDescription}</p>
              </Card>

              {/* Key Points - layout gọn hơn */}
              <Card className="p-4 border-red-200">
                <div className="flex items-center gap-2 mb-3">
                  <Star className="h-4 w-4 text-red-600" />
                  <h3 className="text-lg font-semibold text-red-800">Điểm quan trọng</h3>
                </div>
                <div className="space-y-2">
                  {event.detailContent.keyPoints.map((point, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-red-50 rounded-lg border border-red-100">
                      <div className="w-5 h-5 bg-red-600 text-white rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold">{index + 1}</span>
                      </div>
                      <p className="text-sm text-gray-700">{point}</p>
                    </div>
                  ))}
                </div>
              </Card>
            </div>

            <div className="space-y-4">
              {/* Quick Info - gọn hơn */}
              <Card className="p-4 border-yellow-200 bg-yellow-50">
                <h3 className="text-base font-semibold mb-3 text-yellow-800 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Thông tin
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Năm:</span>
                    <Badge variant="outline" className="text-xs">
                      {event.year}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Loại:</span>
                    <Badge variant="outline" className="text-xs">
                      {event.category}
                    </Badge>
                  </div>
                </div>
              </Card>

              {/* Documents - gọn hơn */}
              <Card className="p-4 border-red-200">
                <div className="flex items-center gap-2 mb-3">
                  <FileText className="h-4 w-4 text-red-600" />
                  <h3 className="text-base font-semibold text-red-800">Tài liệu</h3>
                </div>
                <div className="space-y-1">
                  {event.detailContent.documents.map((doc, index) => (
                    <div
                      key={index}
                      className="p-2 bg-red-50 rounded text-xs text-gray-700 hover:bg-red-100 transition-colors cursor-pointer border border-red-100"
                    >
                      {doc}
                    </div>
                  ))}
                </div>
              </Card>

              {/* Navigation - đơn giản */}
              <Button
                variant="outline"
                className="w-full bg-white hover:bg-red-50 border-red-200 text-red-700"
                onClick={onBack}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
