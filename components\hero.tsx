import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, Target, BookOpen, Users, Sparkles } from "lucide-react"

export function Hero() {
  return (
    <section className="min-h-screen flex items-center justify-center py-16 bg-gradient-to-br from-red-50 via-yellow-50 to-red-50 relative overflow-hidden">
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-32 h-32 bg-red-500 rounded-full animate-float"></div>
        <div
          className="absolute top-40 right-20 w-24 h-24 bg-yellow-500 rounded-full animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute bottom-20 left-1/4 w-20 h-20 bg-red-600 rounded-full animate-float"
          style={{ animationDelay: "4s" }}
        ></div>
        <div
          className="absolute bottom-40 right-1/3 w-28 h-28 bg-yellow-600 rounded-full animate-float"
          style={{ animationDelay: "1s" }}
        ></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          <Badge variant="secondary" className="mb-8 text-lg font-bold px-6 py-2 animate-glow">
            <Sparkles className="w-5 h-5 mr-2" />
            Chủ nghĩa Marx-Lenin
          </Badge>

          <h1 className="text-6xl md:text-8xl font-black text-balance mb-8 hero-text-shadow animate-fade-in-up">
            Quá độ lên{" "}
            <span className="text-red-600 bg-gradient-to-r from-red-600 via-yellow-500 to-red-600 bg-clip-text text-transparent animate-pulse-glow">
              Chủ nghĩa Xã hội
            </span>{" "}
            ở Việt Nam
          </h1>

          <p className="text-2xl text-gray-700 text-balance mb-12 max-w-4xl mx-auto leading-relaxed font-medium animate-slide-in-left">
            Hành trình lịch sử vĩ đại từ xã hội phong kiến thực dân đến xã hội xã hội chủ nghĩa dưới sự lãnh đạo của
            Đảng Cộng sản Việt Nam
          </p>

          <div className="mb-12 animate-slide-in-right">
            <div className="relative inline-block">
              <img
                src="/ho-chi-minh-portrait-with-vietnamese-independence-.jpg"
                alt="Chủ tịch Hồ Chí Minh và Tuyên ngôn Độc lập"
                className="mx-auto rounded-2xl shadow-2xl max-w-lg w-full transform hover:scale-105 transition-transform duration-500"
              />
              <div className="absolute -inset-4 bg-gradient-to-r from-red-500 to-yellow-500 rounded-2xl opacity-20 blur-xl animate-pulse"></div>
            </div>
          </div>

          <Card
            className="p-8 text-left max-w-5xl mx-auto glass-effect border-2 border-red-200 animate-fade-in-up"
            style={{ animationDelay: "0.5s" }}
          >
            <div className="flex items-center gap-3 mb-6 justify-center">
              <Target className="w-8 h-8 text-red-600" />
              <h3 className="text-2xl font-bold text-red-800">Mục tiêu thuyết trình</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex items-start gap-4 p-4 bg-red-50 rounded-lg hover:bg-red-100 transition-colors">
                  <BookOpen className="w-7 h-7 text-red-600 mt-1 flex-shrink-0" />
                  <span className="text-lg font-medium">
                    Hiểu rõ lý thuyết Marx-Lenin về quá độ lên chủ nghĩa xã hội
                  </span>
                </div>
                <div className="flex items-start gap-4 p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                  <Users className="w-7 h-7 text-yellow-600 mt-1 flex-shrink-0" />
                  <span className="text-lg font-medium">
                    Nắm vững các giai đoạn lịch sử của Việt Nam trong quá trình chuyển đổi
                  </span>
                </div>
              </div>
              <div className="space-y-6">
                <div className="flex items-start gap-4 p-4 bg-red-50 rounded-lg hover:bg-red-100 transition-colors">
                  <Star className="w-7 h-7 text-red-600 mt-1 flex-shrink-0" />
                  <span className="text-lg font-medium">Phân tích ứng dụng lý thuyết vào thực tiễn Việt Nam</span>
                </div>
                <div className="flex items-start gap-4 p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                  <Target className="w-7 h-7 text-yellow-600 mt-1 flex-shrink-0" />
                  <span className="text-lg font-medium">
                    Đánh giá thành tựu và thách thức trong quá trình xây dựng CNXH
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </section>
  )
}
