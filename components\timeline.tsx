"use client"

import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Users, Flag, Hammer, Star, Zap, Building, Play, Sparkles } from "lucide-react"
import { useState } from "react"
import { EventDetail } from "./event-detail"

const timelineEvents = [
  {
    id: "1930",
    year: "1930",
    title: "Thành lập Đảng Cộng sản Việt Nam",
    description:
      "Nguyễn Ái <PERSON>uốc thành lập Đảng <PERSON>ộng sản Việt Nam tại <PERSON>, đ<PERSON>h dấu bước ngoặt trong việc ứng dụng lý thuyết Marx-Lenin vào thực tiễn Việt Nam. Đảng ra đời trong bối cảnh phong trào cách mạng thế giới và nhu cầu giải phóng dân tộc.",
    icon: Flag,
    category: "Chính trị",
    image: "/vietnamese-communist-party-founding-ceremony-1930-.jpg",
    significance: "<PERSON><PERSON><PERSON> dấu sự ra đời của lực lượng lãnh đạo cách mạng Việt Nam",
    detailContent: {
      fullDescription:
        "Ngày 3/2/1930, tại Hồng Kông, Nguyễn Ái Quốc đã chủ trì Hội nghị hợp nhất ba tổ chức cộng sản: Đông Dương Cộng sản Đảng, An Nam Cộng sản Đảng và Đông Dương Cộng sản Liên đoàn thành Đảng Cộng sản Việt Nam. Đây là sự kiện có ý nghĩa lịch sử to lớn, đánh dấu bước ngoặt quyết định trong lịch sử cách mạng Việt Nam.",
      keyPoints: [
        "Thống nhất các tổ chức cộng sản phân tán",
        "Xác định con đường cách mạng vô sản",
        "Ứng dụng chủ nghĩa Marx-Lenin vào điều kiện Việt Nam",
        "Tạo nền tảng lý luận cho cách mạng Việt Nam",
      ],
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ", // Placeholder video
      documents: [
        "Cương l영 chính trị đầu tiên của Đảng",
        "Sách lược đầu tiên của Đảng",
        "Thư của Nguyễn Ái Quốc gửi quốc tế cộng sản",
      ],
    },
  },
  {
    id: "1945",
    year: "1945",
    title: "Cách mạng Tháng Tám thành công",
    description:
      "Nhân dân Việt Nam dưới sự lãnh đạo của Đảng Cộng sản giành được chính quyền, thành lập nước Việt Nam Dân chủ Cộng hòa. Chủ tịch Hồ Chí Minh đọc Tuyên ngôn Độc lập tại Quảng trường Ba Đình, khẳng định quyền độc lập của dân tộc.",
    icon: Users,
    category: "Cách mạng",
    image: "/ho-chi-minh-reading-independence-declaration-at-ba.jpg",
    significance: "Bước đầu tiên của quá trình chuyển đổi lên chủ nghĩa xã hội",
    detailContent: {
      fullDescription:
        "Cách mạng Tháng Tám 1945 là cuộc cách mạng dân tộc dân chủ nhân dân do Đảng Cộng sản Đông Dương lãnh đạo, lật đổ ách thống trị của thực dân Pháp và phong kiến Việt Nam, giành chính quyền về tay nhân dân, thành lập nhà nước Việt Nam Dân chủ Cộng hòa.",
      keyPoints: [
        "Tổng khởi nghĩa toàn quốc thành công",
        "Thành lập chính quyền cách mạng",
        "Tuyên bố độc lập ngày 2/9/1945",
        "Bắt đầu quá trình xây dựng nhà nước mới",
      ],
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      documents: ["Tuyên ngôn Độc lập", "Hiến pháp 1946", "Các sắc lệnh đầu tiên của Chính phủ"],
    },
  },
  {
    id: "1954",
    year: "1954",
    title: "Chiến thắng Điện Biên Phủ",
    description:
      "Quân và dân ta giành chiến thắng lịch sử tại Điện Biên Phủ, kết thúc ách thống trị của thực dân Pháp. Hiệp định Genève được ký kết, tạo điều kiện cho việc xây dựng chế độ xã hội chủ nghĩa ở miền Bắc và chuẩn bị thống nhất đất nước.",
    icon: Hammer,
    category: "Quân sự",
    image: "/dien-bien-phu-victory-1954--vietnamese-soldiers-ce.jpg",
    significance: "Mở ra giai đoạn xây dựng CNXH ở miền Bắc Việt Nam",
    detailContent: {
      fullDescription:
        "Chiến dịch Điện Biên Phủ (13/3 - 7/5/1954) là chiến dịch quân sự quyết định, đánh bại hoàn toàn Tập đoàn cứ điểm Điện Biên Phủ của thực dân Pháp, buộc Pháp phải ký Hiệp định Genève, chấm dứt chiến tranh Đông Dương lần thứ nhất.",
      keyPoints: [
        "Chiến thắng quân sự vĩ đại",
        "Kết thúc ách thống trị thực dân Pháp",
        "Ký kết Hiệp định Genève 1954",
        "Mở đường xây dựng CNXH ở miền Bắc",
      ],
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      documents: [
        "Hiệp định Genève 1954",
        "Báo cáo chiến thắng của Đại tướng Võ Nguyên Giáp",
        "Tuyên bố của Chính phủ về chiến thắng",
      ],
    },
  },
  {
    id: "1975",
    year: "1975",
    title: "Thống nhất đất nước",
    description:
      "Hoàn thành cuộc kháng chiến chống Mỹ cứu nước, giải phóng hoàn toàn miền Nam, thống nhất đất nước. Việt Nam trở thành một quốc gia độc lập, thống nhất, tạo điều kiện xây dựng chủ nghĩa xã hội trên toàn quốc.",
    icon: Star,
    category: "Thống nhất",
    image: "/vietnam-reunification-1975--tanks-entering-saigon-.jpg",
    significance: "Hoàn thành nhiệm vụ giải phóng dân tộc, thống nhất đất nước",
    detailContent: {
      fullDescription:
        "Ngày 30/4/1975, Sài Gòn được giải phóng hoàn toàn, đánh dấu kết thúc cuộc kháng chiến chống Mỹ cứu nước và thống nhất đất nước. Việt Nam trở thành một quốc gia hoàn toàn độc lập, thống nhất.",
      keyPoints: [
        "Giải phóng hoàn toàn miền Nam",
        "Thống nhất đất nước sau 21 năm chia cắt",
        "Kết thúc chiến tranh Việt Nam",
        "Mở ra giai đoạn xây dựng CNXH toàn quốc",
      ],
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      documents: ["Tuyên bố thống nhất đất nước", "Hiến pháp 1980", "Nghị quyết về xây dựng CNXH"],
    },
  },
  {
    id: "1986",
    year: "1986",
    title: "Đổi mới kinh tế",
    description:
      "Đại hội VI của Đảng đề ra chính sách Đổi mới, chuyển từ kinh tế kế hoạch hóa tập trung sang kinh tế thị trường định hướng xã hội chủ nghĩa. Đây là bước đột phá trong tư duy phát triển kinh tế - xã hội.",
    icon: Zap,
    category: "Kinh tế",
    image: "/vietnam-economic-reform-1986--modern-factories-and.jpg",
    significance: "Mở ra giai đoạn phát triển mới của đất nước",
    detailContent: {
      fullDescription:
        "Đại hội VI của Đảng (tháng 12/1986) đã đề ra đường lối Đổi mới toàn diện đất nước, chuyển từ cơ chế kế hoạch hóa tập trung bao cấp sang cơ chế thị trường có sự quản lý của nhà nước theo định hướng xã hội chủ nghĩa.",
      keyPoints: [
        "Đổi mới tư duy kinh tế",
        "Phát triển kinh tế thị trường định hướng XHCN",
        "Mở cửa hội nhập quốc tế",
        "Cải cách thể chế chính trị - xã hội",
      ],
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      documents: ["Nghị quyết Đại hội VI", "Luật Đầu tư nước ngoài 1987", "Hiến pháp 1992"],
    },
  },
  {
    id: "2021",
    year: "2021",
    title: "Đại hội XIII của Đảng",
    description:
      "Xác định mục tiêu xây dựng Việt Nam thành nước phát triển, thu nhập cao theo định hướng xã hội chủ nghĩa đến năm 2045. Đề ra chiến lược phát triển bền vững, hiện đại hóa đất nước trong thời đại mới.",
    icon: Building,
    category: "Hiện đại",
    image: "/modern-vietnam-2021--skyscrapers--technology--deve.jpg",
    significance: "Định hướng xây dựng Việt Nam hiện đại, phát triển",
    detailContent: {
      fullDescription:
        "Đại hội XIII của Đảng (tháng 1/2021) đã xác định tầm nhìn đến năm 2045: Việt Nam trở thành nước phát triển, thu nhập cao theo định hướng xã hội chủ nghĩa; dân giàu, nước mạnh, dân chủ, công bằng, văn minh.",
      keyPoints: [
        "Tầm nhìn 2045: nước phát triển, thu nhập cao",
        "Phát triển bền vững, hiện đại hóa",
        "Xây dựng nhà nước pháp quyền XHCN",
        "Hội nhập quốc tế sâu rộng",
      ],
      videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
      documents: ["Nghị quyết Đại hội XIII", "Chiến lược phát triển KT-XH 2021-2030", "Tầm nhìn 2045"],
    },
  },
]

export function Timeline() {
  const [selectedEvent, setSelectedEvent] = useState<(typeof timelineEvents)[0] | null>(null)
  const [isTransitioning, setIsTransitioning] = useState(false)

  const handleEventClick = (event: (typeof timelineEvents)[0]) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setSelectedEvent(event)
      setIsTransitioning(false)
    }, 500)
  }

  const handleBackToTimeline = () => {
    setIsTransitioning(true)
    setTimeout(() => {
      setSelectedEvent(null)
      setIsTransitioning(false)
    }, 500)
  }

  if (selectedEvent) {
    return <EventDetail event={selectedEvent} onBack={handleBackToTimeline} isTransitioning={isTransitioning} />
  }

  return (
    <section
      id="timeline"
      className="py-20 bg-gradient-to-br from-red-50 via-yellow-50 to-red-50 relative overflow-hidden"
    >
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-40 h-40 bg-red-500 rounded-full animate-float"></div>
        <div
          className="absolute top-60 right-20 w-32 h-32 bg-yellow-500 rounded-full animate-float"
          style={{ animationDelay: "3s" }}
        ></div>
        <div
          className="absolute bottom-40 left-1/4 w-28 h-28 bg-red-600 rounded-full animate-float"
          style={{ animationDelay: "1s" }}
        ></div>
      </div>

      <div
        className={`fixed inset-0 bg-gradient-to-r from-red-600/30 via-yellow-500/30 to-red-600/30 z-50 transition-all duration-1000 ${
          isTransitioning ? "opacity-100 scale-100" : "opacity-0 scale-0 pointer-events-none"
        }`}
      >
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_transparent_0%,_rgba(0,0,0,0.9)_100%)]">
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-white animate-fade-in-up">
              <div className="relative mb-6">
                <div className="w-20 h-20 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto"></div>
                <div
                  className="absolute inset-0 w-20 h-20 border-4 border-yellow-400 border-b-transparent rounded-full animate-spin mx-auto"
                  style={{ animationDirection: "reverse", animationDuration: "0.8s" }}
                ></div>
              </div>
              <p className="text-2xl font-bold mb-2">Đang xuyên không thời gian...</p>
              <p className="text-lg opacity-80">Khám phá chi tiết lịch sử</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 animate-fade-in-up">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Sparkles className="w-8 h-8 text-red-600 animate-pulse" />
              <h2 className="text-5xl md:text-6xl font-black text-red-800 hero-text-shadow">Dòng thời gian lịch sử</h2>
              <Sparkles className="w-8 h-8 text-yellow-600 animate-pulse" />
            </div>
            <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed font-medium">
              Hành trình vĩ đại từ cách mạng dân tộc dân chủ đến xây dựng chủ nghĩa xã hội tại Việt Nam
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-4 md:left-1/2 top-0 bottom-0 w-2 bg-gradient-to-b from-red-600 via-yellow-500 to-red-600 transform md:-translate-x-1 rounded-full animate-glow"></div>

            <div className="space-y-16">
              {timelineEvents.map((event, index) => {
                const Icon = event.icon
                const isEven = index % 2 === 0

                return (
                  <div
                    key={event.year}
                    className={`relative flex items-center animate-fade-in-up ${isEven ? "md:flex-row" : "md:flex-row-reverse"}`}
                    style={{ animationDelay: `${index * 0.2}s` }}
                  >
                    <div className="absolute left-4 md:left-1/2 w-6 h-6 bg-gradient-to-r from-red-600 to-yellow-500 rounded-full transform -translate-x-3 md:-translate-x-3 z-10 shadow-2xl border-4 border-white animate-pulse-glow"></div>

                    {/* Content */}
                    <div className={`w-full md:w-1/2 ${isEven ? "md:pr-12" : "md:pl-12"}`}>
                      <Card
                        className="p-8 ml-12 md:ml-0 hover:shadow-2xl transition-all duration-500 hover:scale-105 border-2 border-red-200 cursor-pointer group bg-white/90 backdrop-blur-sm"
                        onClick={() => handleEventClick(event)}
                      >
                        <div className="mb-6 relative overflow-hidden rounded-xl">
                          <img
                            src={event.image || "/placeholder.svg"}
                            alt={event.title}
                            className="w-full h-56 object-cover transition-transform duration-500 group-hover:scale-110"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                            <div className="bg-white/20 backdrop-blur-sm rounded-full p-3 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                              <Play className="h-8 w-8 text-white" />
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 mb-4">
                          <div className="p-3 bg-gradient-to-r from-red-500 to-yellow-500 rounded-xl shadow-lg">
                            <Icon className="h-7 w-7 text-white" />
                          </div>
                          <Badge
                            variant="outline"
                            className="border-red-300 text-red-700 px-3 py-1 text-sm font-semibold"
                          >
                            {event.category}
                          </Badge>
                        </div>

                        <div className="mb-4">
                          <span className="text-5xl font-black text-transparent bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text">
                            {event.year}
                          </span>
                        </div>

                        <h3 className="text-2xl font-bold mb-4 text-balance group-hover:text-red-700 transition-colors leading-tight">
                          {event.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed mb-6 text-base">{event.description}</p>

                        <div className="border-t-2 border-red-100 pt-4 mb-6">
                          <p className="text-sm font-bold text-red-700 mb-2 flex items-center gap-2">
                            <Star className="w-4 h-4" />Ý nghĩa lịch sử:
                          </p>
                          <p className="text-sm text-gray-600 italic leading-relaxed">{event.significance}</p>
                        </div>

                        <Button className="w-full bg-gradient-to-r from-red-600 to-yellow-600 hover:from-red-700 hover:to-yellow-700 text-white font-semibold py-3 text-base shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                          <Play className="h-5 w-5 mr-2" />
                          Khám phá chi tiết & Xem Video
                        </Button>
                      </Card>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
