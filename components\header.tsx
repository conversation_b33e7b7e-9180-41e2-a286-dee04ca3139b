"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X } from "lucide-react"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const scrollToSection = (id: string) => {
    document.getElementById(id)?.scrollIntoView({ behavior: "smooth" })
    setIsMenuOpen(false)
  }

  return (
    <header className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">VN</span>
            </div>
            <h1 className="text-xl font-bold text-foreground">Quá độ lên <PERSON></h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <button
              onClick={() => scrollToSection("timeline")}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Dòng thời gian
            </button>
            <button
              onClick={() => scrollToSection("theory")}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Lý thuyết
            </button>
            <button
              onClick={() => scrollToSection("application")}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Ứng dụng
            </button>
            <button
              onClick={() => scrollToSection("resources")}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Tài liệu
            </button>
          </nav>

          {/* Mobile Menu Button */}
          <Button variant="ghost" size="sm" className="md:hidden" onClick={() => setIsMenuOpen(!isMenuOpen)}>
            {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t border-border pt-4">
            <div className="flex flex-col space-y-3">
              <button
                onClick={() => scrollToSection("timeline")}
                className="text-left text-muted-foreground hover:text-foreground transition-colors"
              >
                Dòng thời gian
              </button>
              <button
                onClick={() => scrollToSection("theory")}
                className="text-left text-muted-foreground hover:text-foreground transition-colors"
              >
                Lý thuyết
              </button>
              <button
                onClick={() => scrollToSection("application")}
                className="text-left text-muted-foreground hover:text-foreground transition-colors"
              >
                Ứng dụng
              </button>
              <button
                onClick={() => scrollToSection("resources")}
                className="text-left text-muted-foreground hover:text-foreground transition-colors"
              >
                Tài liệu
              </button>
            </div>
          </nav>
        )}
      </div>
    </header>
  )
}
