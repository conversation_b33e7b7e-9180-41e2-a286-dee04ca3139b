@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color scheme for academic socialism theme */
  --background: oklch(1 0 0); /* White background */
  --foreground: oklch(0.25 0 0); /* Dark gray text */
  --card: oklch(0.98 0.01 15); /* Light red tint for cards */
  --card-foreground: oklch(0.25 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.25 0 0);
  --primary: oklch(0.55 0.22 25); /* Red primary color */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.75 0.15 65); /* Gold accent */
  --secondary-foreground: oklch(0.25 0 0);
  --muted: oklch(0.98 0.01 15);
  --muted-foreground: oklch(0.45 0 0);
  --accent: oklch(0.75 0.15 65); /* Gold highlight */
  --accent-foreground: oklch(0.25 0 0);
  --destructive: oklch(0.55 0.22 25);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9 0.01 15);
  --input: oklch(1 0 0);
  --ring: oklch(0.55 0.22 25 / 0.5);
  --chart-1: oklch(0.55 0.22 25); /* Red */
  --chart-2: oklch(0.75 0.15 65); /* Gold */
  --chart-3: oklch(0.35 0.05 240); /* Dark blue */
  --chart-4: oklch(0.65 0.12 45); /* Orange */
  --chart-5: oklch(0.45 0.18 15); /* Dark red */
  --radius: 0.5rem;
  --sidebar: oklch(0.98 0.01 15);
  --sidebar-foreground: oklch(0.25 0 0);
  --sidebar-primary: oklch(0.55 0.22 25);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.75 0.15 65);
  --sidebar-accent-foreground: oklch(0.25 0 0);
  --sidebar-border: oklch(0.9 0.01 15);
  --sidebar-ring: oklch(0.55 0.22 25 / 0.5);
}

.dark {
  --background: oklch(0.1 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.15 0.02 15);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.1 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.65 0.22 25);
  --primary-foreground: oklch(0.1 0 0);
  --secondary: oklch(0.25 0.05 65);
  --secondary-foreground: oklch(0.95 0 0);
  --muted: oklch(0.15 0.02 15);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.25 0.05 65);
  --accent-foreground: oklch(0.95 0 0);
  --destructive: oklch(0.45 0.18 25);
  --destructive-foreground: oklch(0.85 0.15 25);
  --border: oklch(0.25 0.02 15);
  --input: oklch(0.25 0.02 15);
  --ring: oklch(0.45 0.15 25 / 0.5);
  --chart-1: oklch(0.65 0.22 25);
  --chart-2: oklch(0.75 0.15 65);
  --chart-3: oklch(0.55 0.15 240);
  --chart-4: oklch(0.65 0.18 45);
  --chart-5: oklch(0.55 0.18 15);
  --sidebar: oklch(0.15 0.02 15);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: oklch(0.65 0.22 25);
  --sidebar-primary-foreground: oklch(0.1 0 0);
  --sidebar-accent: oklch(0.25 0.05 65);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.25 0.02 15);
  --sidebar-ring: oklch(0.45 0.15 25 / 0.5);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Thêm animation và effects cho thuyết trình */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(220, 38, 38, 0.6);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 30px rgba(220, 38, 38, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 60px rgba(220, 38, 38, 0.8);
    transform: scale(1.05);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-slide-in-left {
  animation: slideInLeft 1s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 1s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 1s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 4s ease-in-out infinite;
}

.presentation-gradient {
  background: linear-gradient(
    135deg,
    rgba(220, 38, 38, 0.1) 0%,
    rgba(251, 191, 36, 0.1) 50%,
    rgba(220, 38, 38, 0.1) 100%
  );
}

.hero-text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
